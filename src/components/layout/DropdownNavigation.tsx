import React, { useState, useCallback } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { ChevronDown, ChevronRight } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '../../utils/cn'

export interface NavigationItem {
  path: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  children?: NavigationItem[]
}

interface DropdownNavigationProps {
  item: NavigationItem
  isCollapsed: boolean
  level?: number
}

/**
 * Dropdown Navigation Component
 * Handles hierarchical navigation with expand/collapse functionality
 * Supports nested menu items with proper active state management
 */
const DropdownNavigation: React.FC<DropdownNavigationProps> = ({
  item,
  isCollapsed,
  level = 0
}) => {
  const [isExpanded, setIsExpanded] = useState(false)
  const location = useLocation()

  // Check if current item or any child is active
  const isActive = useCallback((path: string, children?: NavigationItem[]): boolean => {
    if (location.pathname === path) return true
    if (children) {
      return children.some(child => isActive(child.path, child.children))
    }
    return false
  }, [location.pathname])

  // Check if this item or any child is currently active
  const itemIsActive = isActive(item.path, item.children)
  const hasActiveChild = item.children?.some(child => isActive(child.path, child.children))

  // Auto-expand if has active child
  React.useEffect(() => {
    if (hasActiveChild && !isExpanded) {
      setIsExpanded(true)
    }
  }, [hasActiveChild, isExpanded])

  const handleToggle = useCallback((e: React.MouseEvent) => {
    if (item.children && item.children.length > 0) {
      e.preventDefault()
      setIsExpanded(!isExpanded)
    }
  }, [item.children, isExpanded])

  const hasChildren = item.children && item.children.length > 0

  return (
    <div className="w-full">
      {/* Main Navigation Item */}
      <div className="relative">
        <Link 
          to={item.path}
          onClick={hasChildren ? handleToggle : undefined}
          className="block w-full"
        >
          <div
            className={cn(
              'flex items-center gap-2 rounded-md transition-all duration-200',
              'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground',
              itemIsActive && 'bg-sidebar-primary text-sidebar-primary-foreground',
              !itemIsActive && 'text-sidebar-foreground',
              // Responsive padding with indentation for nested items
              isCollapsed
                ? 'px-1.5 py-1.5 sm:px-2 sm:py-2 justify-center'
                : 'px-2 sm:px-2.5 lg:px-3 py-1.5 sm:py-2'
            )}
          >
            {/* Icon */}
            <item.icon className={cn(
              "flex-shrink-0 transition-all duration-200",
              isCollapsed ? "h-4 w-4 sm:h-5 sm:w-5" : "h-4 w-4 lg:h-5 lg:w-5"
            )} />

            {/* Label and Expand/Collapse Icon */}
            <AnimatePresence>
              {!isCollapsed && (
                <motion.div
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                  className="flex items-center justify-between flex-1 min-w-0"
                >
                  <span className="font-medium text-sm lg:text-base truncate">
                    {item.label}
                  </span>
                  
                  {/* Dropdown Arrow for items with children */}
                  {hasChildren && (
                    <motion.div
                      animate={{ rotate: isExpanded ? 90 : 0 }}
                      transition={{ duration: 0.2 }}
                      className="flex-shrink-0 ml-2"
                    >
                      <ChevronRight className="h-3 w-3 lg:h-4 lg:w-4" />
                    </motion.div>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </Link>
      </div>

      {/* Submenu Items */}
      <AnimatePresence>
        {hasChildren && isExpanded && !isCollapsed && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <div className="ml-2 border-l border-sidebar-border/50 pl-2 mt-1 space-y-0.5">
              {item.children.map((child) => (
                <DropdownNavigation
                  key={child.path}
                  item={child}
                  isCollapsed={isCollapsed}
                  level={level + 1}
                />
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default DropdownNavigation
