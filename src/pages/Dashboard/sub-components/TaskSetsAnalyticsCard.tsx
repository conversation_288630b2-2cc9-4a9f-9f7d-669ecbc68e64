import React, { useRef, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'
import { RefreshCw, BarChart3, Database } from 'lucide-react'
import * as echarts from 'echarts'
import { TaskSetsMetrics } from '../../../services/management/managementService'

interface TaskSetsAnalyticsCardProps {
  data: TaskSetsMetrics | null
  loading: boolean
  error: string | null
  onRefresh: () => void
  cardVariants: any
}

// New Bar Chart Component using ECharts
const TaskSetsBarChart: React.FC<{ data: TaskSetsMetrics }> = ({ data }) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts | null>(null)

  const initChart = useCallback(() => {
    if (!chartRef.current || !data?.hierarchy) return

    // Dispose existing chart
    if (chartInstance.current) {
      chartInstance.current.dispose()
    }

    // Create new chart
    chartInstance.current = echarts.init(chartRef.current)

    // Prepare data for bar chart
    const categories = data.hierarchy.map(item => item.generation_type)
    const taskSetsData = data.hierarchy.map(item => item.task_sets_count)
    const taskItemsData = data.hierarchy.map(item => item.task_items_count)

    // ECharts configuration for bar chart
    const option: echarts.EChartsOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function(params: any) {
          let result = `<div style="padding: 8px; background: rgba(0,0,0,0.9); border-radius: 6px; color: white;">
            <div style="font-weight: bold; margin-bottom: 4px;">${params[0].axisValue}</div>`
          params.forEach((param: any) => {
            result += `<div style="margin: 2px 0;">
              <span style="display: inline-block; width: 10px; height: 10px; background: ${param.color}; border-radius: 50%; margin-right: 6px;"></span>
              ${param.seriesName}: <strong>${param.value}</strong>
            </div>`
          })
          result += '</div>'
          return result
        }
      },
      legend: {
        data: ['Task Sets', 'Task Items'],
        top: 10,
        textStyle: {
          color: '#6b7280',
          fontSize: 12
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: categories,
        axisLine: {
          lineStyle: {
            color: '#e5e7eb'
          }
        },
        axisLabel: {
          color: '#6b7280',
          fontSize: 11,
          interval: 0,
          rotate: 0
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#6b7280',
          fontSize: 11
        },
        splitLine: {
          lineStyle: {
            color: '#f3f4f6',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          name: 'Task Sets',
          type: 'bar',
          data: taskSetsData,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: '#3b82f6' },
                { offset: 1, color: '#1d4ed8' }
              ]
            },
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(59, 130, 246, 0.5)'
            }
          }
        },
        {
          name: 'Task Items',
          type: 'bar',
          data: taskItemsData,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: '#10b981' },
                { offset: 1, color: '#047857' }
              ]
            },
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(16, 185, 129, 0.5)'
            }
          }
        }
      ]
    }

    chartInstance.current.setOption(option)

    // Handle resize
    const handleResize = () => {
      chartInstance.current?.resize()
    }
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [data])

  useEffect(() => {
    const cleanup = initChart()
    return cleanup
  }, [initChart])

  useEffect(() => {
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose()
      }
    }
  }, [])

  return (
    <div className="h-full w-full">
      <div
        ref={chartRef}
        style={{ height: '100%', width: '100%' }}
      />
    </div>
  )
}

/**
 * Task Sets Analytics Card - Clean bar chart visualization
 */
const TaskSetsAnalyticsCard: React.FC<TaskSetsAnalyticsCardProps> = ({
  data,
  loading,
  error,
  onRefresh,
  cardVariants
}) => {

  if (loading) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden h-full"
      >
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-muted/20 to-transparent animate-pulse" />
        <div className="relative space-y-6 h-full">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-5 bg-muted rounded w-32 animate-pulse" />
              <div className="h-4 bg-muted rounded w-48 animate-pulse" />
            </div>
            <div className="h-8 w-8 bg-muted rounded animate-pulse" />
          </div>
          <div className="flex-1 bg-muted rounded animate-pulse" />
        </div>
      </motion.div>
    )
  }

  if (error) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden h-full"
      >
        <div className="relative space-y-6 h-full">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Task Sets Analytics
              </h3>
              <p className="text-sm text-muted-foreground">Generation type comparison</p>
            </div>
            <button
              onClick={onRefresh}
              className="p-2 hover:bg-muted rounded-lg transition-colors"
              title="Refresh data"
            >
              <RefreshCw className="h-4 w-4 text-muted-foreground" />
            </button>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center space-y-2">
              <p className="text-destructive">Failed to load chart data</p>
              <button
                onClick={onRefresh}
                className="text-sm text-primary hover:underline"
              >
                Try again
              </button>
            </div>
          </div>
        </div>
      </motion.div>
    )
  }

  if (!data) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden h-full"
      >
        <div className="relative space-y-6 h-full">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Task Sets Analytics
              </h3>
              <p className="text-sm text-muted-foreground">Generation type comparison</p>
            </div>
            <button
              onClick={onRefresh}
              className="p-2 hover:bg-muted rounded-lg transition-colors"
              title="Refresh data"
            >
              <RefreshCw className="h-4 w-4 text-muted-foreground" />
            </button>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center space-y-2">
              <Database className="h-12 w-12 text-muted-foreground mx-auto" />
              <p className="text-muted-foreground">No data available</p>
            </div>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      variants={cardVariants}
      className="bg-card border border-border rounded-xl p-6 relative overflow-hidden h-full"
    >
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-transparent to-green-50/50 rounded-xl" />

      <div className="relative space-y-6 h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              Task Sets Analytics
            </h3>
            <p className="text-sm text-muted-foreground">Generation type comparison</p>
          </div>
          <button
            onClick={onRefresh}
            className="p-2 hover:bg-muted rounded-lg transition-colors group"
            title="Refresh data"
          >
            <RefreshCw className="h-4 w-4 text-muted-foreground group-hover:rotate-180 transition-transform duration-500" />
          </button>
        </div>

        {/* Chart Container */}
        <div className="flex-1 min-h-0">
          <TaskSetsBarChart data={data} />
        </div>
      </div>
    </motion.div>
  )
}

export default TaskSetsAnalyticsCard