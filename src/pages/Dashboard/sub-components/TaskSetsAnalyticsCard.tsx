import React, { useRef, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'
import { RefreshCw } from 'lucide-react'
import * as echarts from 'echarts'
import type { TaskSetsMetrics } from '../../../services/management/managementService'

interface TaskSetsAnalyticsCardProps {
  data: TaskSetsMetrics | null
  loading: boolean
  error: string | null
  onRefresh: () => void
  cardVariants: any
}



// Task Sets Sunburst Chart Component using ECharts
const TaskSetsSunburstChart: React.FC<{ data: TaskSetsMetrics }> = ({ data }) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts | null>(null)

  // Extract sunburst data and calculate min/max for VisualMap
  const getSunburstData = useCallback((metrics: TaskSetsMetrics) => {
    if (!metrics.sunburst_data || !Array.isArray(metrics.sunburst_data)) {
      console.warn('Missing or invalid sunburst_data:', metrics)
      return { data: [], min: 0, max: 100 }
    }

    // Calculate min and max values for VisualMap from the data
    let allValues: number[] = []

    const extractValues = (items: any[]) => {
      items.forEach(item => {
        if (item.value) allValues.push(item.value)
        if (item.children) extractValues(item.children)
      })
    }

    extractValues(metrics.sunburst_data)

    const min = allValues.length > 0 ? Math.min(...allValues) : 0
    const max = allValues.length > 0 ? Math.max(...allValues) : 100

    console.log('Sunburst data from API:', { data: metrics.sunburst_data, min, max })
    return { data: metrics.sunburst_data, min, max }
  }, [])

  // Initialize and update chart
  const initChart = useCallback(() => {
    if (!chartRef.current || !data) return

    // Dispose existing chart
    if (chartInstance.current) {
      chartInstance.current.dispose()
    }

    // Create new chart
    chartInstance.current = echarts.init(chartRef.current)

    const sunburstResult = getSunburstData(data)

    const option: echarts.EChartsOption = {
      tooltip: {
        trigger: 'item',
        formatter: function(params: any) {
          const { name, value, data } = params
          const isLeaf = !data.children || data.children.length === 0
          return `
            <div style="padding: 12px; background: rgba(0,0,0,0.9); border-radius: 8px; color: white; box-shadow: 0 4px 12px rgba(0,0,0,0.3);">
              <div style="font-weight: bold; margin-bottom: 6px; font-size: 14px;">${name}</div>
              <div style="font-size: 13px;">${isLeaf ? 'Task Items' : 'Task Sets'}: <strong>${value}</strong></div>
            </div>
          `
        }
      },
      visualMap: {
        type: 'continuous',
        min: sunburstResult.min,
        max: sunburstResult.max,
        inRange: {
          color: ['#2F93C8', '#AEC48F', '#FFDB5C', '#F98862']
        }
      },
      series: {
        type: 'sunburst',
        data: sunburstResult.data,
        radius: [0, '90%'],
        label: {
          rotate: 'radial'
        }
      }
    }

    chartInstance.current.setOption(option)

    // Handle resize
    const handleResize = () => {
      chartInstance.current?.resize()
    }
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [data, getSunburstData])

  useEffect(() => {
    const cleanup = initChart()
    return cleanup
  }, [initChart])

  useEffect(() => {
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose()
      }
    }
  }, [])

  return (
    <div className="h-full w-full">
      <div
        ref={chartRef}
        style={{ height: '100%', width: '100%' }}
      />
    </div>
  )
}



/**
 * Task Sets Analytics Card - Displays task sets creation and completion trends
 * Features animated charts and type distributions
 */
const TaskSetsAnalyticsCard: React.FC<TaskSetsAnalyticsCardProps> = ({
  data,
  loading,
  error,
  onRefresh,
  cardVariants
}) => {

  if (loading) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden"
      >
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-muted/20 to-transparent animate-pulse" />
        <div className="relative space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-5 bg-muted rounded w-32 animate-pulse" />
              <div className="h-4 bg-muted rounded w-48 animate-pulse" />
            </div>
            <div className="h-8 w-8 bg-muted rounded animate-pulse" />
          </div>
          <div className="grid grid-cols-3 gap-4 mb-6">
            <div className="h-20 bg-muted rounded animate-pulse" />
            <div className="h-20 bg-muted rounded animate-pulse" />
            <div className="h-20 bg-muted rounded animate-pulse" />
          </div>
          <div className="h-96 bg-muted rounded animate-pulse" />
        </div>
      </motion.div>
    )
  }

  if (error) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-destructive/20 rounded-xl p-6"
      >
        <div className="text-center space-y-4">
          <div className="text-destructive font-medium">Failed to load task sets analytics</div>
          <p className="text-sm text-muted-foreground">{error}</p>
          <motion.button
            onClick={onRefresh}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            <RefreshCw className="h-4 w-4 inline mr-2" />
            Retry
          </motion.button>
        </div>
      </motion.div>
    )
  }

  if (!data) return null

  return (
    <motion.div
      variants={cardVariants}
      className="bg-card border border-border rounded-xl p-4 h-full"
    >
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-foreground">Task Distribution</h3>
          <motion.button
            onClick={onRefresh}
            whileHover={{ scale: 1.1, rotate: 180 }}
            whileTap={{ scale: 0.9 }}
            className="p-2 hover:bg-muted rounded-md transition-colors"
            title="Refresh analytics"
          >
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </motion.button>
        </div>

        {/* Sunburst Chart - Full height */}
        <div className="flex-1 min-h-0 bg-gradient-to-br from-orange-50/30 to-red-50/30 dark:from-orange-900/10 dark:to-red-900/10 rounded-lg p-3">
          <TaskSetsSunburstChart data={data} />
        </div>
      </div>
    </motion.div>
  )
}

export default TaskSetsAnalyticsCard
