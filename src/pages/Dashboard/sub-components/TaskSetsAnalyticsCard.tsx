import React, { useMemo } from 'react'
import { motion } from 'framer-motion'
import { RefreshCw, Target, Database } from 'lucide-react'
import { ResponsiveSunburst } from '@nivo/sunburst'
import { TaskSetsMetrics } from '../../../services/management/managementService'

interface TaskSetsAnalyticsCardProps {
  data: TaskSetsMetrics | null
  loading: boolean
  error: string | null
  onRefresh: () => void
  cardVariants: any
}

// Color scheme for generation types
const GENERATION_COLORS = {
  primary: '#3B82F6',     // Blue
  curated: '#10B981',     // Green
  follow_up: '#F59E0B'    // Orange
}

// Generate child colors based on parent color
const generateChildColors = (parentColor: string, childCount: number) => {
  const colors = []
  for (let i = 0; i < childCount; i++) {
    // Create variations of parent color (lighter/darker)
    const opacity = 0.4 + (i * 0.6 / Math.max(childCount - 1, 1))
    colors.push(`${parentColor}${Math.round(opacity * 255).toString(16).padStart(2, '0')}`)
  }
  return colors
}

// Transform data for Nivo Sunburst
const transformDataForSunburst = (data: TaskSetsMetrics) => {
  if (!data?.hierarchy) return null

  const sunburstData = {
    name: 'Task Sets',
    color: '#6B7280',
    children: data.hierarchy.map((genType) => {
      const parentColor = GENERATION_COLORS[genType.generation_type as keyof typeof GENERATION_COLORS] || '#6B7280'
      const childColors = generateChildColors(parentColor, genType.task_item_types?.length || 0)

      return {
        name: genType.generation_type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        value: genType.task_sets_count,
        color: parentColor,
        children: genType.task_item_types?.map((taskType, index) => ({
          name: taskType.task_item_type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
          value: Math.max(taskType.task_items_count, 1), // Ensure minimum value of 1 for visibility
          color: childColors[index] || parentColor,
          originalValue: taskType.task_items_count
        })) || []
      }
    })
  }

  return sunburstData
}

// Nivo Sunburst Component
const TaskSetsSunburst: React.FC<{ data: TaskSetsMetrics }> = ({ data }) => {
  const sunburstData = useMemo(() => transformDataForSunburst(data), [data])

  if (!sunburstData) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <div className="text-center space-y-2">
          <Database className="h-12 w-12 text-muted-foreground mx-auto" />
          <p className="text-muted-foreground">No data available</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full w-full">
      <ResponsiveSunburst
        data={sunburstData}
        margin={{ top: 10, right: 10, bottom: 10, left: 10 }}
        id="name"
        value="value"
        cornerRadius={3}
        borderWidth={2}
        borderColor="#ffffff"
        colors={(node: any) => node.data.color}
        childColor={{
          from: 'color',
          modifiers: [['opacity', 0.8]]
        }}
        enableArcLabels={true}
        arcLabel={(node: any) => {
          // Show label only if arc is large enough
          const percentage = (node.value / node.parent?.value || 1) * 100
          if (percentage < 5) return '' // Hide labels for small arcs
          return node.id
        }}
        arcLabelsRadiusOffset={0.5}
        arcLabelsTextColor="#ffffff"
        arcLabelsSkipAngle={10}
        tooltip={({ id, value, color, data }: any) => (
          <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-lg border">
            <div className="flex items-center gap-2 mb-2">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: color }}
              />
              <span className="font-semibold">{id}</span>
            </div>
            <div className="text-sm">
              <div>Value: <strong>{data.originalValue !== undefined ? data.originalValue : value}</strong></div>
              {data.originalValue !== undefined && data.originalValue !== value && (
                <div className="text-xs text-muted-foreground">
                  (Displayed as {value} for visibility)
                </div>
              )}
            </div>
          </div>
        )}
        animate={true}
        motionConfig="gentle"
      />
    </div>
  )
}

/**
 * Task Sets Analytics Card - Clean bar chart visualization
 */
const TaskSetsAnalyticsCard: React.FC<TaskSetsAnalyticsCardProps> = ({
  data,
  loading,
  error,
  onRefresh,
  cardVariants
}) => {

  if (loading) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden h-full"
      >
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-muted/20 to-transparent animate-pulse" />
        <div className="relative space-y-6 h-full">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-5 bg-muted rounded w-32 animate-pulse" />
              <div className="h-4 bg-muted rounded w-48 animate-pulse" />
            </div>
            <div className="h-8 w-8 bg-muted rounded animate-pulse" />
          </div>
          <div className="flex-1 bg-muted rounded animate-pulse" />
        </div>
      </motion.div>
    )
  }

  if (error) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden h-full"
      >
        <div className="relative space-y-6 h-full">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                <Target className="h-5 w-5" />
                Task Sets Distribution
              </h3>
              <p className="text-sm text-muted-foreground">Hierarchical sunburst view</p>
            </div>
            <button
              onClick={onRefresh}
              className="p-2 hover:bg-muted rounded-lg transition-colors"
              title="Refresh data"
            >
              <RefreshCw className="h-4 w-4 text-muted-foreground" />
            </button>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center space-y-2">
              <p className="text-destructive">Failed to load chart data</p>
              <button
                onClick={onRefresh}
                className="text-sm text-primary hover:underline"
              >
                Try again
              </button>
            </div>
          </div>
        </div>
      </motion.div>
    )
  }

  if (!data) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden h-full"
      >
        <div className="relative space-y-6 h-full">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                <Target className="h-5 w-5" />
                Task Sets Distribution
              </h3>
              <p className="text-sm text-muted-foreground">Hierarchical sunburst view</p>
            </div>
            <button
              onClick={onRefresh}
              className="p-2 hover:bg-muted rounded-lg transition-colors"
              title="Refresh data"
            >
              <RefreshCw className="h-4 w-4 text-muted-foreground" />
            </button>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center space-y-2">
              <Database className="h-12 w-12 text-muted-foreground mx-auto" />
              <p className="text-muted-foreground">No data available</p>
            </div>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      variants={cardVariants}
      className="bg-card border border-border rounded-xl p-6 relative overflow-hidden h-full"
    >
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-transparent to-green-50/50 rounded-xl" />

      <div className="relative space-y-6 h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
              <Target className="h-5 w-5 text-blue-600" />
              Task Sets Distribution
            </h3>
            <p className="text-sm text-muted-foreground">Hierarchical sunburst view</p>
          </div>
          <button
            onClick={onRefresh}
            className="p-2 hover:bg-muted rounded-lg transition-colors group"
            title="Refresh data"
          >
            <RefreshCw className="h-4 w-4 text-muted-foreground group-hover:rotate-180 transition-transform duration-500" />
          </button>
        </div>

        {/* Chart Container */}
        <div className="flex-1 min-h-0">
          <TaskSetsSunburst data={data} />
        </div>
      </div>
    </motion.div>
  )
}

export default TaskSetsAnalyticsCard