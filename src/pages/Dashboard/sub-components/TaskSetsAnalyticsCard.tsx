import React, { useRef, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'
import { RefreshCw } from 'lucide-react'
import * as echarts from 'echarts'
import type { TaskSetsMetrics } from '../../../services/management/managementService'

interface TaskSetsAnalyticsCardProps {
  data: TaskSetsMetrics | null
  loading: boolean
  error: string | null
  onRefresh: () => void
  cardVariants: any
}

// Chart colors
const COLORS = ['#3B82F6', '#10B981', '#8B5CF6', '#F59E0B', '#EF4444', '#06B6D4', '#84CC16', '#F97316']

// Task Sets Sunburst Chart Component using ECharts
const TaskSetsSunburstChart: React.FC<{ data: TaskSetsMetrics }> = ({ data }) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts | null>(null)

  // Transform data for sunburst chart
  const transformDataForSunburst = useCallback((metrics: TaskSetsMetrics) => {
    if (!metrics.hierarchy || !Array.isArray(metrics.hierarchy)) {
      console.warn('Missing or invalid hierarchy data:', metrics)
      return []
    }

    const transformedData = metrics.hierarchy.map((genType, genIndex) => ({
      name: genType.generation_type,
      value: genType.task_sets_count,
      itemStyle: {
        color: COLORS[genIndex % COLORS.length]
      },
      children: genType.task_item_types?.map((taskType) => ({
        name: taskType.task_item_type,
        value: taskType.task_items_count,
        itemStyle: {
          color: echarts.color.modifyHSL(COLORS[genIndex % COLORS.length], null, null, 0.7)
        }
      })) || []
    }))

    console.log('Sunburst data transformed:', transformedData)
    return transformedData
  }, [])

  // Initialize and update chart
  const initChart = useCallback(() => {
    if (!chartRef.current || !data) return

    // Dispose existing chart
    if (chartInstance.current) {
      chartInstance.current.dispose()
    }

    // Create new chart
    chartInstance.current = echarts.init(chartRef.current)

    const sunburstData = transformDataForSunburst(data)

    const option: echarts.EChartsOption = {
      tooltip: {
        trigger: 'item',
        formatter: function(params: any) {
          const { name, value, data } = params
          const isLeaf = !data.children || data.children.length === 0
          return `
            <div style="padding: 8px; background: rgba(0,0,0,0.8); border-radius: 6px; color: white;">
              <div style="font-weight: bold; margin-bottom: 4px;">${name}</div>
              <div>${isLeaf ? 'Task Items' : 'Task Sets'}: ${value}</div>
            </div>
          `
        }
      },
      legend: {
        type: 'scroll',
        orient: 'horizontal',
        bottom: 10,
        data: data.hierarchy?.map(item => item.generation_type) || [],
        textStyle: {
          color: '#6b7280',
          fontSize: 11
        }
      },
      series: [{
        type: 'sunburst',
        data: sunburstData,
        radius: [20, '85%'],
        center: ['50%', '45%'],
        sort: undefined,
        emphasis: {
          focus: 'ancestor',
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          }
        },
        levels: [
          {},
          {
            r0: '20%',
            r: '50%',
            itemStyle: {
              borderWidth: 2,
              borderColor: '#fff',
              shadowBlur: 8,
              shadowColor: 'rgba(0, 0, 0, 0.2)'
            },
            label: {
              show: true,
              position: 'inside',
              fontSize: 14,
              fontWeight: 'bold',
              color: '#000',
              textShadowColor: 'rgba(255, 255, 255, 0.8)',
              textShadowBlur: 2,
              formatter: function(params: any) {
                return params.name.length > 10 ? params.name.substring(0, 10) + '...' : params.name
              }
            }
          },
          {
            r0: '50%',
            r: '85%',
            itemStyle: {
              borderWidth: 1,
              borderColor: '#fff',
              shadowBlur: 5,
              shadowColor: 'rgba(0, 0, 0, 0.15)'
            },
            label: {
              show: true,
              position: 'inside',
              fontSize: 12,
              color: '#000',
              textShadowColor: 'rgba(255, 255, 255, 0.8)',
              textShadowBlur: 1,
              formatter: function(params: any) {
                return params.name.length > 8 ? params.name.substring(0, 8) + '...' : params.name
              }
            }
          },
          {
            r0: '75%',
            r: '98%',
            itemStyle: {
              borderWidth: 1,
              borderColor: '#fff',
              shadowBlur: 5,
              shadowColor: 'rgba(0, 0, 0, 0.1)'
            },
            label: {
              show: true,
              position: 'inside',
              fontSize: 8,
              color: '#fff',
              textShadowColor: 'rgba(0, 0, 0, 0.5)',
              textShadowBlur: 1,
              formatter: function(params: any) {
                return params.name.length > 4 ? params.name.substring(0, 4) + '...' : params.name
              }
            }
          }
        ],
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: function () {
          return Math.random() * 200
        }
      }]
    }

    chartInstance.current.setOption(option)

    // Handle resize
    const handleResize = () => {
      chartInstance.current?.resize()
    }
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [data, transformDataForSunburst])

  useEffect(() => {
    const cleanup = initChart()
    return cleanup
  }, [initChart])

  useEffect(() => {
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose()
      }
    }
  }, [])

  return (
    <div className="h-full w-full">
      <div
        ref={chartRef}
        style={{ height: '100%', width: '100%' }}
      />
    </div>
  )
}



/**
 * Task Sets Analytics Card - Displays task sets creation and completion trends
 * Features animated charts and type distributions
 */
const TaskSetsAnalyticsCard: React.FC<TaskSetsAnalyticsCardProps> = ({
  data,
  loading,
  error,
  onRefresh,
  cardVariants
}) => {

  if (loading) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden"
      >
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-muted/20 to-transparent animate-pulse" />
        <div className="relative space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-5 bg-muted rounded w-32 animate-pulse" />
              <div className="h-4 bg-muted rounded w-48 animate-pulse" />
            </div>
            <div className="h-8 w-8 bg-muted rounded animate-pulse" />
          </div>
          <div className="grid grid-cols-3 gap-4 mb-6">
            <div className="h-20 bg-muted rounded animate-pulse" />
            <div className="h-20 bg-muted rounded animate-pulse" />
            <div className="h-20 bg-muted rounded animate-pulse" />
          </div>
          <div className="h-96 bg-muted rounded animate-pulse" />
        </div>
      </motion.div>
    )
  }

  if (error) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-destructive/20 rounded-xl p-6"
      >
        <div className="text-center space-y-4">
          <div className="text-destructive font-medium">Failed to load task sets analytics</div>
          <p className="text-sm text-muted-foreground">{error}</p>
          <motion.button
            onClick={onRefresh}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            <RefreshCw className="h-4 w-4 inline mr-2" />
            Retry
          </motion.button>
        </div>
      </motion.div>
    )
  }

  if (!data) return null

  return (
    <motion.div
      variants={cardVariants}
      className="bg-card border border-border rounded-xl p-4 h-full"
    >
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-foreground">Task Distribution</h3>
          <motion.button
            onClick={onRefresh}
            whileHover={{ scale: 1.1, rotate: 180 }}
            whileTap={{ scale: 0.9 }}
            className="p-2 hover:bg-muted rounded-md transition-colors"
            title="Refresh analytics"
          >
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </motion.button>
        </div>

        {/* Sunburst Chart - Full height */}
        <div className="flex-1 min-h-0 bg-gradient-to-br from-orange-50/30 to-red-50/30 dark:from-orange-900/10 dark:to-red-900/10 rounded-lg p-3">
          <TaskSetsSunburstChart data={data} />
        </div>
      </div>
    </motion.div>
  )
}

export default TaskSetsAnalyticsCard
