import React, { useRef, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'
import { RefreshCw, TrendingUp } from 'lucide-react'
import * as echarts from 'echarts'
import { TaskSetsMetrics } from '../../../services/management/managementService'

interface TaskSetsAnalyticsCardProps {
  data: TaskSetsMetrics | null
  loading: boolean
  error: string | null
  onRefresh: () => void
  cardVariants: any
}

// Sunburst Chart Component using ECharts with VisualMap
const TaskSetsSunburstChart: React.FC<{ data: TaskSetsMetrics }> = ({ data }) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts | null>(null)

  const initChart = useCallback(() => {
    if (!chartRef.current || !data?.sunburst_data) return

    // Dispose existing chart
    if (chartInstance.current) {
      chartInstance.current.dispose()
    }

    // Create new chart
    chartInstance.current = echarts.init(chartRef.current)

    // Calculate min and max values for VisualMap
    let allValues: number[] = []

    const extractValues = (items: any[]) => {
      items.forEach(item => {
        if (item.value) allValues.push(item.value)
        if (item.children) extractValues(item.children)
      })
    }

    extractValues(data.sunburst_data)

    const min = allValues.length > 0 ? Math.min(...allValues) : 0
    const max = allValues.length > 0 ? Math.max(...allValues) : 10

    // ECharts configuration exactly like the example
    const option: echarts.EChartsOption = {
      visualMap: {
        type: 'continuous',
        min: min,
        max: max,
        inRange: {
          color: ['#2F93C8', '#AEC48F', '#FFDB5C', '#F98862']
        }
      },
      series: {
        type: 'sunburst',
        data: data.sunburst_data,
        radius: [0, '90%'],
        label: {
          rotate: 'radial'
        }
      }
    }

    chartInstance.current.setOption(option)

    // Handle resize
    const handleResize = () => {
      chartInstance.current?.resize()
    }
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [data])

  useEffect(() => {
    const cleanup = initChart()
    return cleanup
  }, [initChart])

  useEffect(() => {
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose()
      }
    }
  }, [])

  return (
    <div className="h-full w-full">
      <div
        ref={chartRef}
        style={{ height: '100%', width: '100%' }}
      />
    </div>
  )
}

/**
 * Task Sets Analytics Card - Displays task sets sunburst chart with VisualMap
 */
const TaskSetsAnalyticsCard: React.FC<TaskSetsAnalyticsCardProps> = ({
  data,
  loading,
  error,
  onRefresh,
  cardVariants
}) => {

  if (loading) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden h-full"
      >
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-muted/20 to-transparent animate-pulse" />
        <div className="relative space-y-6 h-full">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-5 bg-muted rounded w-32 animate-pulse" />
              <div className="h-4 bg-muted rounded w-48 animate-pulse" />
            </div>
            <div className="h-8 w-8 bg-muted rounded animate-pulse" />
          </div>
          <div className="flex-1 bg-muted rounded animate-pulse" />
        </div>
      </motion.div>
    )
  }

  if (error) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden h-full"
      >
        <div className="relative space-y-6 h-full">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-foreground">Task Sets Distribution</h3>
              <p className="text-sm text-muted-foreground">Hierarchical view of task sets and items</p>
            </div>
            <button
              onClick={onRefresh}
              className="p-2 hover:bg-muted rounded-lg transition-colors"
              title="Refresh data"
            >
              <RefreshCw className="h-4 w-4 text-muted-foreground" />
            </button>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center space-y-2">
              <p className="text-destructive">Failed to load chart data</p>
              <button
                onClick={onRefresh}
                className="text-sm text-primary hover:underline"
              >
                Try again
              </button>
            </div>
          </div>
        </div>
      </motion.div>
    )
  }

  if (!data) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden h-full"
      >
        <div className="relative space-y-6 h-full">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-foreground">Task Sets Distribution</h3>
              <p className="text-sm text-muted-foreground">Hierarchical view of task sets and items</p>
            </div>
            <button
              onClick={onRefresh}
              className="p-2 hover:bg-muted rounded-lg transition-colors"
              title="Refresh data"
            >
              <RefreshCw className="h-4 w-4 text-muted-foreground" />
            </button>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center space-y-2">
              <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto" />
              <p className="text-muted-foreground">No data available</p>
            </div>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      variants={cardVariants}
      className="bg-card border border-border rounded-xl p-6 relative overflow-hidden h-full"
    >
      {/* Glassmorphism background effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 rounded-xl" />

      {/* Animated border effect */}
      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-primary/20 via-transparent to-secondary/20 opacity-0 hover:opacity-100 transition-opacity duration-500" />

      <div className="relative space-y-6 h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-foreground">Task Sets Distribution</h3>
            <p className="text-sm text-muted-foreground">Hierarchical view of task sets and items</p>
          </div>
          <button
            onClick={onRefresh}
            className="p-2 hover:bg-muted rounded-lg transition-colors group"
            title="Refresh data"
          >
            <RefreshCw className="h-4 w-4 text-muted-foreground group-hover:rotate-180 transition-transform duration-500" />
          </button>
        </div>

        {/* Chart Container */}
        <div className="flex-1 min-h-0">
          <TaskSetsSunburstChart data={data} />
        </div>
      </div>
    </motion.div>
  )
}

export default TaskSetsAnalyticsCard