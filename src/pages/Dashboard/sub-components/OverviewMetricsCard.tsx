import React from 'react'
import { motion } from 'framer-motion'
import { Users, UserPlus, Activity, FileText, Plus, CheckCircle, TrendingUp, RefreshCw } from 'lucide-react'
import { cn } from '../../../utils/cn'
import type { DashboardOverview } from '../../../services/management/managementService'

// Modern card animations
const modernCardStyles = `
  @keyframes subtle-glow {
    0%, 100% {
      box-shadow: 0 0 0 1px var(--border-color), 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    50% {
      box-shadow: 0 0 0 1px var(--border-color), 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  @keyframes border-flow {
    0% {
      border-color: var(--border-color);
    }
    50% {
      border-color: var(--border-hover-color);
    }
    100% {
      border-color: var(--border-color);
    }
  }

  .modern-card {
    transition: all 0.3s ease;
    border-width: 2px;
    border-style: solid;
  }

  .modern-card:hover {
    animation: subtle-glow 2s ease-in-out infinite;
    transform: translateY(-2px);
  }
`

interface OverviewMetricsCardProps {
  data: DashboardOverview | null
  taskSetsData: any | null
  loading: boolean
  error: string | null
  onRefresh: () => void
  cardVariants: any
}

/**
 * Overview Metrics Card - Displays key dashboard metrics
 * Sleek design with animated counters and hover effects
 */
const OverviewMetricsCard: React.FC<OverviewMetricsCardProps> = ({
  data,
  taskSetsData,
  loading,
  error,
  onRefresh,
  cardVariants
}) => {
  if (loading) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden"
      >
        {/* Animated loading background */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-muted/20 to-transparent animate-pulse" />
        <div className="relative space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-5 bg-muted rounded w-32 animate-pulse" />
              <div className="h-4 bg-muted rounded w-48 animate-pulse" />
            </div>
            <div className="h-8 w-8 bg-muted rounded animate-pulse" />
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: i * 0.1 }}
                className="bg-background border border-border rounded-lg p-4 space-y-3"
              >
                <div className="flex items-center gap-3">
                  <div className="h-8 w-8 bg-muted rounded-lg animate-pulse" />
                </div>
                <div className="space-y-2">
                  <div className="h-6 bg-muted rounded w-12 animate-pulse" />
                  <div className="h-3 bg-muted rounded w-20 animate-pulse" />
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.div>
    )
  }

  if (error) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-destructive/20 rounded-xl p-6"
      >
        <div className="text-center space-y-4">
          <div className="text-destructive font-medium">Failed to load metrics</div>
          <p className="text-sm text-muted-foreground">{error}</p>
          <motion.button
            onClick={onRefresh}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            <RefreshCw className="h-4 w-4 inline mr-2" />
            Retry
          </motion.button>
        </div>
      </motion.div>
    )
  }

  if (!data) return null

  const metrics = [
    {
      label: 'Total Users',
      value: data.overview.total_users,
      icon: Users,
      borderColor: 'border-blue-500',
      bgColor: 'bg-blue-500/5 dark:bg-blue-500/10',
      iconColor: 'text-blue-500',
      textColor: 'text-blue-600 dark:text-blue-400'
    },
    {
      label: 'Joined Today',
      value: data.overview.users_joined_today,
      icon: UserPlus,
      borderColor: 'border-emerald-500',
      bgColor: 'bg-emerald-500/5 dark:bg-emerald-500/10',
      iconColor: 'text-emerald-500',
      textColor: 'text-emerald-600 dark:text-emerald-400'
    },
    {
      label: 'Active Today',
      value: data.overview.users_active_today,
      icon: Activity,
      borderColor: 'border-purple-500',
      bgColor: 'bg-purple-500/5 dark:bg-purple-500/10',
      iconColor: 'text-purple-500',
      textColor: 'text-purple-600 dark:text-purple-400'
    },
    {
      label: 'Total Task Sets',
      value: data.overview.total_task_sets,
      icon: FileText,
      borderColor: 'border-orange-500',
      bgColor: 'bg-orange-500/5 dark:bg-orange-500/10',
      iconColor: 'text-orange-500',
      textColor: 'text-orange-600 dark:text-orange-400'
    },
    {
      label: 'Created Today',
      value: data.overview.task_sets_created_today,
      icon: Plus,
      borderColor: 'border-teal-500',
      bgColor: 'bg-teal-500/5 dark:bg-teal-500/10',
      iconColor: 'text-teal-500',
      textColor: 'text-teal-600 dark:text-teal-400'
    },
    {
      label: 'Completed Today',
      value: data.overview.task_sets_completed_today,
      icon: CheckCircle,
      borderColor: 'border-green-500',
      bgColor: 'bg-green-500/5 dark:bg-green-500/10',
      iconColor: 'text-green-500',
      textColor: 'text-green-600 dark:text-green-400'
    }
  ]

  // Modern MetricCard component matching the design
  const MetricCard = ({ metric, index }: { metric: any, index: number }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        delay: index * 0.1,
        duration: 0.4,
        ease: "easeOut"
      }}
      whileHover={{
        scale: 1.02,
        y: -2,
        transition: { duration: 0.2 }
      }}
      className={cn(
        "modern-card rounded-xl p-4 h-full cursor-pointer",
        metric.borderColor,
        metric.bgColor
      )}
      style={{
        '--border-color': `rgb(${metric.borderColor.includes('blue') ? '59 130 246' :
                                  metric.borderColor.includes('emerald') ? '16 185 129' :
                                  metric.borderColor.includes('purple') ? '139 92 246' :
                                  '245 158 11'})`,
        '--border-hover-color': `rgba(${metric.borderColor.includes('blue') ? '59 130 246' :
                                        metric.borderColor.includes('emerald') ? '16 185 129' :
                                        metric.borderColor.includes('purple') ? '139 92 246' :
                                        '245 158 11'}, 0.8)`
      } as React.CSSProperties}
    >
      {/* Header with icon and label */}
      <div className="flex items-center gap-2 mb-3">
        <motion.div
          whileHover={{ scale: 1.1 }}
          className={cn("p-1.5 rounded-lg", metric.iconColor)}
        >
          <metric.icon className="h-4 w-4" />
        </motion.div>
        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
          {metric.label}
        </span>
      </div>

      {/* Large number display */}
      <motion.div
        className={cn("text-3xl font-bold", metric.textColor)}
        initial={{ scale: 0.8 }}
        animate={{ scale: 1 }}
        transition={{ delay: index * 0.1 + 0.2, type: "spring", stiffness: 200 }}
      >
        {metric.value.toLocaleString()}
      </motion.div>
    </motion.div>
  )

  return (
    <>
      <style>{modernCardStyles}</style>
      <motion.div
        variants={cardVariants}
        className="bg-transparent p-0"
      >
        {/* Metrics Grid - 2x3 layout for 6 cards */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
          {metrics.map((metric, index) => (
            <MetricCard key={metric.label} metric={metric} index={index} />
          ))}
        </div>

        {/* Refresh button and timestamp */}
        <div className="flex items-center justify-between">
          <motion.div
            className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
          >
            <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
            Last updated: {new Date(data.timestamp).toLocaleString()}
          </motion.div>

          <motion.button
            onClick={onRefresh}
            whileHover={{ scale: 1.1, rotate: 180 }}
            whileTap={{ scale: 0.9 }}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
            title="Refresh metrics"
          >
            <RefreshCw className="h-4 w-4 text-gray-500 dark:text-gray-400" />
          </motion.button>
        </div>
      </motion.div>
    </>
  )
}

export default OverviewMetricsCard
