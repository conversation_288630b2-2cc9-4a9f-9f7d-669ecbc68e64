import httpBase, { RequestCallbacks } from '../http/httpBase'

// Types for Management API responses
export interface DashboardOverview {
  timestamp: string
  overview: {
    total_users: number
    users_joined_today: number
    users_active_today: number
    total_task_sets: number
    task_sets_created_today: number
    task_sets_completed_today: number
  }
}

export interface UsersMetrics {
  timestamp: string
  date_range: {
    start_date: string
    end_date: string
  }
  summary: {
    total_users: number
    users_in_period: number
  }
  daily_data: {
    registrations: Array<{
      _id: string
      count: number
    }>
    active_users: Array<{
      _id: string
      count: number
    }>
  }
  distribution: {
    by_role: Array<{
      _id: string
      count: number
    }>
  }
}

export interface TaskSetsMetrics {
  timestamp: string
  date_range: {
    start_date: string
    end_date: string
  }
  summary: {
    total_task_sets_all_time: number
    total_task_items_all_time: number
    total_task_sets_in_range: number
    total_task_items_in_range: number
  }
  hierarchy: Array<{
    generation_type: string
    task_sets_count: number
    task_items_count: number
    task_item_types: Array<{
      task_item_type: string
      task_items_count: number
    }>
  }>
  // Legacy structure for backward compatibility
  task_sets?: {
    by_generation_type: Array<{
      _id: string
      count: number
    }>
    by_input_type: Array<{
      _id: string | null
      count: number
    }>
    by_status: Array<{
      _id: string
      count: number
    }>
    daily_creation: Array<{
      _id: string
      count: number
    }>
    performance_stats: any[]
  }
  task_items?: {
    by_type: Array<{
      _id: string
      count: number
    }>
    by_verification_status: Array<{
      _id: string | null
      count: number
    }>
  }
}

export interface UserDetails {
  user_id: string
  username: string
  email: string
  role: string
  created_at: string
  last_active: string
  stats: {
    total_task_sets: number
    completed_task_sets: number
    total_score: number
    average_score: number
  }
}

class ManagementService {
  private baseUrl = '/management'

  /**
   * Get dashboard overview with key metrics
   */
  async getDashboardOverview(
    callbacks?: RequestCallbacks<DashboardOverview>
  ): Promise<DashboardOverview> {
    const response = await httpBase.get<DashboardOverview>(
      `${this.baseUrl}/dashboard`,
      {},
      callbacks
    )
    return response.data
  }

  /**
   * Get users metrics and analytics with optional date filtering
   */
  async getUsersMetrics(
    startDate?: string,
    endDate?: string,
    callbacks?: RequestCallbacks<UsersMetrics>
  ): Promise<UsersMetrics> {
    const params = new URLSearchParams()
    if (startDate) params.append('start_date', startDate)
    if (endDate) params.append('end_date', endDate)

    const url = `${this.baseUrl}/dashboard/users${params.toString() ? `?${params.toString()}` : ''}`
    const response = await httpBase.get<UsersMetrics>(url, {}, callbacks)
    return response.data
  }

  /**
   * Get task sets metrics and analytics with optional date filtering
   */
  async getTaskSetsMetrics(
    startDate?: string,
    endDate?: string,
    callbacks?: RequestCallbacks<TaskSetsMetrics>
  ): Promise<TaskSetsMetrics> {
    const params = new URLSearchParams()
    if (startDate) params.append('start_date', startDate)
    if (endDate) params.append('end_date', endDate)

    const url = `${this.baseUrl}/dashboard/task-sets${params.toString() ? `?${params.toString()}` : ''}`
    const response = await httpBase.get<TaskSetsMetrics>(url, {}, callbacks)
    return response.data
  }

  /**
   * Get specific user details (admin only)
   */
  async getUserDetails(
    userId: string,
    callbacks?: RequestCallbacks<UserDetails>
  ): Promise<UserDetails> {
    const response = await httpBase.get<UserDetails>(
      `${this.baseUrl}/admin/dashboard/user/${userId}`,
      {},
      callbacks
    )
    return response.data
  }

  /**
   * Helper method to format metrics for display
   */
  formatMetricsForDisplay(overview: DashboardOverview) {
    return {
      totalUsers: overview.overview.total_users,
      usersJoinedToday: overview.overview.users_joined_today,
      usersActiveToday: overview.overview.users_active_today,
      totalTaskSets: overview.overview.total_task_sets,
      taskSetsCreatedToday: overview.overview.task_sets_created_today,
      taskSetsCompletedToday: overview.overview.task_sets_completed_today,
      timestamp: overview.timestamp
    }
  }

  /**
   * Helper method to format user metrics for charts
   */
  formatUserMetricsForCharts(metrics: UsersMetrics) {
    return {
      registrationData: metrics.daily_data.registrations.map(item => ({
        date: item._id,
        count: item.count
      })),
      activeUserData: metrics.daily_data.active_users.map(item => ({
        date: item._id,
        count: item.count
      })),
      roleDistribution: metrics.distribution.by_role.map(item => ({
        role: item._id,
        count: item.count
      })),
      summary: metrics.summary
    }
  }

  /**
   * Helper method to format task sets metrics for charts
   */
  formatTaskSetsMetricsForCharts(metrics: TaskSetsMetrics) {
    return {
      creationData: metrics.task_sets.daily_creation.map(item => ({
        date: item._id,
        count: item.count
      })),
      generationTypeData: metrics.task_sets.by_generation_type.map(item => ({
        type: item._id,
        count: item.count
      })),
      inputTypeData: metrics.task_sets.by_input_type.map(item => ({
        type: item._id || 'Unknown',
        count: item.count
      })),
      statusData: metrics.task_sets.by_status.map(item => ({
        status: item._id,
        count: item.count
      })),
      taskItemTypes: metrics.task_items.by_type.map(item => ({
        type: item._id,
        count: item.count
      })),
      summary: metrics.summary
    }
  }
}

export const managementService = new ManagementService()
export default managementService
